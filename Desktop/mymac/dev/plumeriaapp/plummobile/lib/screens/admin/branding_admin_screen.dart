import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/branding_provider.dart';
import '../../models/branding_model.dart';

class BrandingAdminScreen extends StatefulWidget {
  const BrandingAdminScreen({super.key});

  @override
  State<BrandingAdminScreen> createState() => _BrandingAdminScreenState();
}

class _BrandingAdminScreenState extends State<BrandingAdminScreen> {
  final _formKey = GlobalKey<FormState>();
  final _adminKeyController = TextEditingController();
  final _appNameController = TextEditingController();
  final _companyNameController = TextEditingController();
  final _taglineController = TextEditingController();
  final _primaryColorController = TextEditingController();
  final _loginWelcomeController = TextEditingController();
  final _loginSubtitleController = TextEditingController();
  final _dashboardWelcomeController = TextEditingController();
  final _dashboardSubtitleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadCurrentBranding();
  }

  void _loadCurrentBranding() {
    final brandingProvider = Provider.of<BrandingProvider>(context, listen: false);
    final branding = brandingProvider.brandingOrDefault;

    _appNameController.text = branding.appName;
    _companyNameController.text = branding.companyName;
    _taglineController.text = branding.tagline ?? '';
    _primaryColorController.text = _colorToHex(branding.theme.primaryColor);
    _loginWelcomeController.text = branding.texts.loginWelcome ?? '';
    _loginSubtitleController.text = branding.texts.loginSubtitle ?? '';
    _dashboardWelcomeController.text = branding.texts.dashboardWelcome ?? '';
    _dashboardSubtitleController.text = branding.texts.dashboardSubtitle ?? '';
  }

  String _colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  Color _colorFromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  @override
  void dispose() {
    _adminKeyController.dispose();
    _appNameController.dispose();
    _companyNameController.dispose();
    _taglineController.dispose();
    _primaryColorController.dispose();
    _loginWelcomeController.dispose();
    _loginSubtitleController.dispose();
    _dashboardWelcomeController.dispose();
    _dashboardSubtitleController.dispose();
    super.dispose();
  }

  Future<void> _updateBranding() async {
    if (!_formKey.currentState!.validate()) return;

    final brandingProvider = Provider.of<BrandingProvider>(context, listen: false);
    
    try {
      final newBranding = BrandingConfig(
        appName: _appNameController.text.trim(),
        companyName: _companyNameController.text.trim(),
        tagline: _taglineController.text.trim().isEmpty ? null : _taglineController.text.trim(),
        theme: ThemeConfig(
          primaryColor: _colorFromHex(_primaryColorController.text),
          secondaryColor: const Color(0xFF764ba2),
          accentColor: const Color(0xFF4CAF50),
          errorColor: const Color(0xFFFF5722),
          backgroundColor: const Color(0xFFF8FAFC),
        ),
        assets: AssetsConfig.defaultAssets(),
        texts: TextsConfig(
          loginWelcome: _loginWelcomeController.text.trim().isEmpty ? null : _loginWelcomeController.text.trim(),
          loginSubtitle: _loginSubtitleController.text.trim().isEmpty ? null : _loginSubtitleController.text.trim(),
          dashboardWelcome: _dashboardWelcomeController.text.trim().isEmpty ? null : _dashboardWelcomeController.text.trim(),
          dashboardSubtitle: _dashboardSubtitleController.text.trim().isEmpty ? null : _dashboardSubtitleController.text.trim(),
        ),
        features: FeaturesConfig.defaultFeatures(),
        version: brandingProvider.branding?.version ?? 1,
      );

      final success = await brandingProvider.updateBranding(
        newBranding,
        _adminKeyController.text.trim(),
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Branding updated successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(brandingProvider.error ?? 'Failed to update branding'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Branding Admin'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Consumer<BrandingProvider>(
        builder: (context, brandingProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Update App Branding',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Admin Key
                  TextFormField(
                    controller: _adminKeyController,
                    decoration: const InputDecoration(
                      labelText: 'Admin Key',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.key),
                    ),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter admin key';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // App Name
                  TextFormField(
                    controller: _appNameController,
                    decoration: const InputDecoration(
                      labelText: 'App Name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.apps),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter app name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Company Name
                  TextFormField(
                    controller: _companyNameController,
                    decoration: const InputDecoration(
                      labelText: 'Company Name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.business),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter company name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Tagline
                  TextFormField(
                    controller: _taglineController,
                    decoration: const InputDecoration(
                      labelText: 'Tagline (Optional)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.text_fields),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Primary Color
                  TextFormField(
                    controller: _primaryColorController,
                    decoration: const InputDecoration(
                      labelText: 'Primary Color (Hex)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.color_lens),
                      hintText: '#667eea',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter primary color';
                      }
                      if (!RegExp(r'^#[0-9A-Fa-f]{6}$').hasMatch(value)) {
                        return 'Please enter valid hex color (e.g., #667eea)';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  const Text(
                    'Custom Texts',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Login Welcome
                  TextFormField(
                    controller: _loginWelcomeController,
                    decoration: const InputDecoration(
                      labelText: 'Login Welcome Text',
                      border: OutlineInputBorder(),
                      hintText: 'Welcome to\\nYour App',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),

                  // Login Subtitle
                  TextFormField(
                    controller: _loginSubtitleController,
                    decoration: const InputDecoration(
                      labelText: 'Login Subtitle',
                      border: OutlineInputBorder(),
                      hintText: 'Your tagline here',
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Dashboard Welcome
                  TextFormField(
                    controller: _dashboardWelcomeController,
                    decoration: const InputDecoration(
                      labelText: 'Dashboard Welcome Text',
                      border: OutlineInputBorder(),
                      hintText: 'Welcome back,',
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Dashboard Subtitle
                  TextFormField(
                    controller: _dashboardSubtitleController,
                    decoration: const InputDecoration(
                      labelText: 'Dashboard Subtitle',
                      border: OutlineInputBorder(),
                      hintText: 'Have a productive day!',
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Update Button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: brandingProvider.isLoading ? null : _updateBranding,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.deepPurple,
                        foregroundColor: Colors.white,
                      ),
                      child: brandingProvider.isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text(
                              'Update Branding',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
